import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { CallToActionCardComponent } from './call-to-action.component';
import { NavigateLinkDirective } from '../../core/mobile/navigate-link.directive';

@NgModule({
  imports: [CommonModule, MatCardModule, MatButtonModule, TranslateModule, NavigateLinkDirective],
  declarations: [CallToActionCardComponent],
  exports: [CallToActionCardComponent, NavigateLinkDirective],
})
export class CallToActionCardModule {}
