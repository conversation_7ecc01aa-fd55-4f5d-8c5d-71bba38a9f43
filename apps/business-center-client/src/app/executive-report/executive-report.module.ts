import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { GoogleMapsModule } from '@angular/google-maps';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RouterModule } from '@angular/router';
import { BusinessCategoryModule } from '@galaxy/business-category';
import { LexiconModule } from '@galaxy/lexicon';
import { TranslateModule } from '@ngx-translate/core';
import { TaxonomyI18nModule } from '@vendasta/taxonomy-i18n';
import { TagCloudComponent } from 'angular-tag-cloud-module';
import { ClickOutsideModule } from 'ng-click-outside';
import { InViewportModule } from 'ng-in-viewport';
import { NgChartsModule } from 'ng2-charts';
import baseTranslation from '../../assets/i18n/en_devel.json';
import { AuthWallModule } from '../auth-wall';
import { WEBLATE_COMPONENT_NAME } from '../constants';
import { DisruptRecommendationsModule } from '../disrupt-recommendations';
import { PageId } from '../page-access';
import { ConnectedCardsModule } from '../performance-dashboard/connected-cards/connected-cards.module';
import { PerformanceDashboardModule } from '../performance-dashboard/performance-dashboard.module';
import { CommonReportHeaderComponent } from '../reports/common-report-header/common-report-header.component';
import { ExecReportBreadcrumbModule } from '../reports/executive-report/exec-report-breadcrumb/exec-report-breadcrumb.component';
import { ReportsModule } from '../reports/reports.module';
import { ExecReportAppSectionComponent } from './app-section.component';
import { CardHeaderComponent, MarkdownDirective } from './basic';
import { BarChartCardComponent } from './cards/bar-chart.component';
import { CallToActionCardModule } from './cards/call-to-action-card.module';
import { LineGraphComponent } from './cards/line-graph.component';
import { NumberWithChangeCardComponent } from './cards/number-with-change.component';
import { FormatNumberPipe } from './cards/number-with-change.pipe';
import { ThermometerComponent } from './cards/thermometer.component';
import { WordCloudComponent } from './cards/word-cloud.component';
import { ExecReportCategoryComponent } from './category.component';
import { DatePickerComponent, DatePickerTabComponent } from './date-picker';
import { ExecutiveReportComponent } from './executive-report.component';
import { ListCardComponent, ListItemComponent } from './list';
import OtherCategoryComponent from './other-category.component';
import { VaPickerModule } from './va-picker/va-picker.module';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { PrintButtonComponent } from './print-button/print-button.component';
import { GalaxyPopoverModule } from '@vendasta/galaxy/popover';
import { PageOptions } from '../navigation/navigation.interface';
import { FunnelMetricsComponent } from '../funnel-metrics/funnel-metrics.component';
import { NavigateLinkDirective } from '../core/mobile/navigate-link.directive';

@NgModule({
  imports: [
    RouterModule,
    CommonModule,
    FormsModule,
    VaPickerModule,
    MatProgressBarModule,
    MatCardModule,
    MatButtonModule,
    MatToolbarModule,
    NgChartsModule,
    MatTooltipModule,
    MatIconModule,
    TagCloudComponent,
    TranslateModule,
    CallToActionCardModule,
    LexiconModule.forChild({
      componentName: WEBLATE_COMPONENT_NAME,
      baseTranslation: baseTranslation,
    }),
    InViewportModule,
    PerformanceDashboardModule,
    ClickOutsideModule,
    ConnectedCardsModule,
    GoogleMapsModule,
    DisruptRecommendationsModule,
    AuthWallModule,
    ReportsModule,
    TaxonomyI18nModule,
    ExecReportBreadcrumbModule,
    BusinessCategoryModule,
    CommonReportHeaderComponent,
    GalaxyPageModule,
    GalaxyPopoverModule,
    PrintButtonComponent,
    FunnelMetricsComponent,
    NavigateLinkDirective,
  ],
  declarations: [
    ExecReportCategoryComponent,
    ExecReportAppSectionComponent,
    NumberWithChangeCardComponent,
    DatePickerComponent,
    DatePickerTabComponent,
    FormatNumberPipe,
    ExecutiveReportComponent,
    CardHeaderComponent,
    MarkdownDirective,
    ListCardComponent,
    ListItemComponent,
    BarChartCardComponent,
    ThermometerComponent,
    LineGraphComponent,
    WordCloudComponent,
    OtherCategoryComponent,
  ],
  exports: [
    ExecutiveReportComponent,
    ThermometerComponent,
    ExecReportAppSectionComponent,
    ExecReportCategoryComponent,
    OtherCategoryComponent,
  ],
})
export class ExecutiveReportModule {}

@NgModule({
  imports: [
    RouterModule.forChild([
      {
        path: 'dynamic',
        loadComponent: () => import('../single-location-executive-report/location-report/location-report.component'),
        data: {
          pageId: PageId.single_location_report,
        },
      },
      {
        path: ':frequency',
        component: ExecutiveReportComponent,
        data: {
          pageId: PageId.executive_report,
          pageOptions: {
            [PageOptions.hasCustomDateRangeInitializer]: true,
          },
        },
      },
      {
        path: ':frequency/:date',
        component: ExecutiveReportComponent,
        data: {
          pageId: PageId.executive_report,
          pageOptions: {
            [PageOptions.hasCustomDateRangeInitializer]: true,
          },
        },
      },
      {
        path: '',
        component: ExecutiveReportComponent,
        data: {
          pageId: PageId.executive_report,
          pageOptions: {
            [PageOptions.hasCustomDateRangeInitializer]: true,
          },
        },
      },
    ]),
    LexiconModule.forChild({
      componentName: WEBLATE_COMPONENT_NAME,
      baseTranslation: baseTranslation,
    }),
  ],
})
export class ExecutiveReportRouteModule {}
