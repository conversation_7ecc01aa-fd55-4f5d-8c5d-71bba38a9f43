import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { GalaxyAlertModule } from '@vendasta/galaxy/alert';
import { GalaxyLoadingSpinnerModule } from '@vendasta/galaxy/loading-spinner';
import { LocalSeoKeywordsStatsCardsModule, LocalSEOTableModule } from '@vendasta/local-seo';
import { ReportsModule } from '../../reports/reports.module';
import { PerformanceDashboardModule } from '../performance-dashboard.module';
import { AdvertisingIntelligenceComponent } from './advertising-intelligence/advertising-intelligence.component';
import { ConnectedCardsComponent } from './connected-cards.component';
import { ConstantContactComponent } from './constant-contact/constant-contact.component';
import { NewGmbCardsComponent } from './google-my-business/new-gmb-cards.component';
import { BingInsightsComponent } from './bing-insights/bing-insights.component';
import { ListingAccuracyAndAcceptanceComponent } from './listing-accuracy-and-acceptance/listing-accuracy-and-acceptance.component';
import { ListingScoreV2Component } from './listing-score-v2/listing-score-v2.component';
import { ListingScoreComponent } from './listing-score/listing-score.component';
import { LocalSeoCardsComponent } from './local-seo-cards/local-seo-cards.component';
import { MultiListingsComponent } from './multi-listings/multi-listings.component';
import { QuickbooksCardComponent } from './quickbooks-card/quickbooks-card.component';
import { RepReviewsCardsComponent } from './rep-reviews-cards/rep-reviews-cards.component';
import { SocialStats2Component } from './social-stats-v2/social-stats.component';
import { MatCardModule } from '@angular/material/card';
import { GalaxyEmptyStateModule } from '@vendasta/galaxy/empty-state';
import { MatIconModule } from '@angular/material/icon';
import { RepNpsCardsComponent } from './rep-nps-cards/rep-nps-cards.component';
import { GalaxyPageModule } from '@vendasta/galaxy/page';
import { NavTabsComponent } from '@vendasta/business-nav';
import { NpsChartOverallScoreComponent, NpsRollingAverageComponent } from '@vendasta/reviews';
import { GalaxyInfiniteScrollTriggerModule } from '@vendasta/galaxy/infinite-scroll-trigger';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatTabsModule } from '@angular/material/tabs';
import { ReactiveFormsModule } from '@angular/forms';
import { CustomerRelationsMetricsComponent } from './customer-relations/customer-relations-metrics.component';
import { RepTeamCardsComponent } from './rep-team-cards/rep-team-cards.component';
import { CallToActionCardModule } from '../../executive-report/cards/call-to-action-card.module';
@NgModule({
  imports: [
    CommonModule,
    CallToActionCardModule,
    RouterModule,
    PerformanceDashboardModule,
    ReportsModule,
    GalaxyLoadingSpinnerModule,
    GalaxyAlertModule,
    TranslateModule,
    LocalSEOTableModule,
    MatCardModule,
    GalaxyEmptyStateModule,
    MatIconModule,
    LocalSeoKeywordsStatsCardsModule,
    LocalSeoCardsComponent,
    GalaxyPageModule,
    GalaxyInfiniteScrollTriggerModule,
    GalaxyEmptyStateModule,
    GalaxyLoadingSpinnerModule,
    MatIconModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatTabsModule,
    ReactiveFormsModule,
    NavTabsComponent,
    NpsRollingAverageComponent,
    NpsChartOverallScoreComponent,
    CustomerRelationsMetricsComponent,
  ],
  declarations: [
    ConnectedCardsComponent,
    NewGmbCardsComponent,
    BingInsightsComponent,
    ConstantContactComponent,
    MultiListingsComponent,
    RepReviewsCardsComponent,
    AdvertisingIntelligenceComponent,
    ListingScoreComponent,
    QuickbooksCardComponent,
    SocialStats2Component,
    ListingScoreV2Component,
    ListingAccuracyAndAcceptanceComponent,
    RepNpsCardsComponent,
    RepTeamCardsComponent,
  ],
  exports: [
    LocalSeoCardsComponent,
    ConnectedCardsComponent,
    ListingScoreComponent,
    NewGmbCardsComponent,
    RepReviewsCardsComponent,
    MultiListingsComponent,
    AdvertisingIntelligenceComponent,
    ListingAccuracyAndAcceptanceComponent,
    ListingScoreV2Component,
    RepNpsCardsComponent,
    RepTeamCardsComponent,
    NavTabsComponent,
    NpsRollingAverageComponent,
    NpsChartOverallScoreComponent,
  ],
})
export class ConnectedCardsModule {}
