@if (!noDataFound && isLocalSEOActive) {
  <div class="anchor-link-position" id="bing-places"></div>
  <h2>Bing Places</h2>
  <div class="new-performance-row">
    @if (viewsMetricsData$ | async) {
      <bc-new-base-card [cardConfig]="viewsMetricsConfig" [dataSource]="viewsMetricsData$ | async"></bc-new-base-card>
    }
    @if (actionsMetricsData$ | async) {
      <bc-new-base-card
        [cardConfig]="actionsMetricsConfig"
        [dataSource]="actionsMetricsData$ | async"
      ></bc-new-base-card>
    }
  </div>
} @else if (isLocalSEOActive && noDataFound) {
  <div class="cta-container">
    <bc-call-to-action-card
      [cardData]="{
        uniqueId: 'cta_bing_connect',
        title: 'Unlock Bing Places Insights',
        description:
          'Activate Bing Places to discover how people interact with your business online. Understand how people are finding you, where they\'re coming from, and how many of them are clicking on your phone number! Once your Bing Places account is connected, you will be able to view online business insights on your next report.',
        templateType: 'CALL_TO_ACTION',
        nextUrlLabel: 'Connect now',
        nextUrl: '/edit/account/' + accountGroupId + '/google-insights/',
        width: 'FULL',
      }"
      appId="MS"
      [buildLink]="buildLink"
    ></bc-call-to-action-card>
  </div>
}
